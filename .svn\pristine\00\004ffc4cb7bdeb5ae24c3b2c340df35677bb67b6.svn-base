{"sql_config": {"enabled": true, "host": "splendiddb.public.d490bb5f4eae.database.windows.net,3342", "port": 1433, "database": "SplendidCRM_TriMicro", "username": "tmwGPT", "password": "ZaQXa$TP6J", "driver": "mssql", "connection_timeout": 30, "query_timeout": 60, "change_request_query": "SELECT [BUG_NUMBER],[NAME],[STATUS],[PRIORITY],[TYPE],[FIXED_IN_RELEASE_RELEASE_GROUP],[FIXED_IN_RELEASE_VERSION],[DESCRIPTION],[ASSIGNED_TO],[CREATED_BY],[MODIFIED_BY],[ASSIGNED_TO_NAME],[TEST_ASSIGNED_TO_NAME_C],[CREATED_BY_NAME],[MODIFIED_BY_NAME],[DATES] FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS] WHERE [BUG_NUMBER] = :change_request_number", "change_request_patterns": ["BUG[#\\-\\s]*(\\d+)", "Bug[#\\-\\s]*(\\d+)", "bug[#\\-\\s]*(\\d+)", "CR[#\\-\\s]*(\\d+)", "Change[#\\-\\s]*(\\d+)", "Request[#\\-\\s]*(\\d+)", "Ticket[#\\-\\s]*(\\d+)", "Issue[#\\-\\s]*(\\d+)", "#(\\d+)", "SPLENDID[#\\-\\s]*(\\d+)", "CRM[#\\-\\s]*(\\d+)"]}}