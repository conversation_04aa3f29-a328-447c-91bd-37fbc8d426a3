#!/usr/bin/env python3
"""
Test script for SplendidCRM SQL Server connection
Tests connection to: splendiddb.public.d490bb5f4eae.database.windows.net,3342
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging

from change_request_service import ChangeRequestService
from models import Config, SqlConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_splendidcrm_connection():
    """Test connection to SplendidCRM SQL Server database"""

    print("🔍 Testing SplendidCRM SQL Server Connection")
    print("=" * 50)

    # Your specific configuration
    sql_config = SqlConfig(
        enabled=True,
        host="splendiddb.public.d490bb5f4eae.database.windows.net,3342",
        port=1433,  # This will be ignored since port is in hostname
        database="SplendidCRM_TriMicro",
        username="tmwGPT",
        password="ZaQXa$TP6J",
        driver="mssql",
        connection_timeout=30,
        query_timeout=60,
    )

    # Create Config object with sql_config
    config = Config()
    config.sql_config = sql_config

    print(f"📊 Server: {sql_config.host}")
    print(f"🗄️ Database: {sql_config.database}")
    print(f"👤 Username: {sql_config.username}")
    print(f"🔐 Password: {'*' * len(sql_config.password)}")
    print(f"⚙️ Driver: {sql_config.driver}")
    print()

    try:
        # Initialize service
        print("🔧 Initializing ChangeRequestService...")
        service = ChangeRequestService(config)

        # Test basic connection
        print("🔌 Testing database connection...")
        connection_result = service.test_connection()

        if connection_result:
            print("✅ Connection successful!")

            # Test database structure exploration
            print("\n🔍 Exploring database structure...")

            # Try to list tables (if permissions allow)
            try:
                with service._get_connection() as conn:
                    cursor = conn.cursor()

                    # Get table list
                    cursor.execute("""
                        SELECT TABLE_NAME 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_TYPE = 'BASE TABLE'
                        ORDER BY TABLE_NAME
                    """)

                    tables = cursor.fetchall()
                    print(f"📋 Found {len(tables)} tables:")

                    for i, (table_name,) in enumerate(
                        tables[:10]
                    ):  # Show first 10 tables
                        print(f"   {i + 1:2d}. {table_name}")

                    if len(tables) > 10:
                        print(f"   ... and {len(tables) - 10} more tables")

                    print()

                    # Look for change request related tables
                    print("🔍 Looking for change request related tables...")
                    change_request_tables = [
                        table
                        for (table,) in tables
                        if any(
                            keyword in table.lower()
                            for keyword in [
                                "change",
                                "request",
                                "ticket",
                                "issue",
                                "task",
                                "bug",
                                "feature",
                            ]
                        )
                    ]

                    if change_request_tables:
                        print("📋 Potential change request tables found:")
                        for table in change_request_tables:
                            print(f"   • {table}")

                            # Get column info for first few tables
                            if len(change_request_tables) <= 3:
                                try:
                                    cursor.execute(f"""
                                        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
                                        FROM INFORMATION_SCHEMA.COLUMNS 
                                        WHERE TABLE_NAME = '{table}'
                                        ORDER BY ORDINAL_POSITION
                                    """)
                                    columns = cursor.fetchall()
                                    print(f"     Columns ({len(columns)}):")
                                    for col_name, data_type, nullable in columns[:5]:
                                        print(
                                            f"       - {col_name} ({data_type}{'?' if nullable == 'YES' else ''})"
                                        )
                                    if len(columns) > 5:
                                        print(
                                            f"       ... and {len(columns) - 5} more columns"
                                        )
                                    print()
                                except Exception as e:
                                    print(f"     Could not get column info: {e}")
                    else:
                        print("❌ No obvious change request tables found")
                        print(
                            "💡 You may need to identify the correct table name manually"
                        )

            except Exception as e:
                print(f"⚠️ Could not explore database structure: {e}")
                print(
                    "💡 This might be due to limited permissions, but connection works!"
                )

        else:
            print("❌ Connection failed!")
            return False

    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        print(f"🔍 Error type: {type(e).__name__}")

        # Provide troubleshooting hints
        print("\n🛠️ Troubleshooting hints:")
        if "Login failed" in str(e):
            print("   • Check username and password")
            print("   • Verify user has access to the database")
        elif "server was not found" in str(e):
            print("   • Check server name and port")
            print("   • Verify network connectivity")
        elif "timeout" in str(e).lower():
            print("   • Check firewall settings")
            print("   • Try increasing connection timeout")
        elif "driver" in str(e).lower():
            print("   • Verify ODBC Driver 18 is installed")
            print("   • Check if running in Docker container")

        return False

    return True


def test_splendidcrm_query():
    """Test the specific SplendidCRM vwBUGS query"""

    print("\n📝 Testing SplendidCRM vwBUGS Query:")
    print("=" * 50)

    sql_config = SqlConfig(
        enabled=True,
        host="splendiddb.public.d490bb5f4eae.database.windows.net,3342",
        port=1433,
        database="SplendidCRM_TriMicro",
        username="tmwGPT",
        password="ZaQXa$TP6J",
        driver="mssql",
        connection_timeout=30,
        query_timeout=60,
    )

    # Create Config object with sql_config
    config = Config()
    config.sql_config = sql_config

    # Your specific SplendidCRM query
    splendid_query = """
SELECT [BUG_NUMBER]
      ,[NAME]
      ,[STATUS]
      ,[PRIORITY]
      ,[TYPE]
      ,[FIXED_IN_RELEASE_RELEASE_GROUP]
      ,[FIXED_IN_RELEASE_VERSION]
      ,[DESCRIPTION]
      ,[ASSIGNED_TO]
      ,[CREATED_BY]
      ,[MODIFIED_BY]
      ,[ASSIGNED_TO_NAME]
      ,[TEST_ASSIGNED_TO_NAME_C]
      ,[CREATED_BY_NAME]
      ,[MODIFIED_BY_NAME]
      ,[DATES]
  FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS]
  WHERE [BUG_NUMBER] = :change_request_number
    """.strip()

    print("🔍 Query to test:")
    print(splendid_query)
    print()

    try:
        service = ChangeRequestService(config)

        # Test the view exists and get sample data
        print("🔌 Testing vwBUGS view access...")

        with service._get_connection() as conn:
            cursor = conn.cursor()

            # First, check if the view exists
            cursor.execute("""
                SELECT COUNT(*) as view_exists
                FROM INFORMATION_SCHEMA.VIEWS
                WHERE TABLE_NAME = 'vwBUGS'
            """)

            view_exists = cursor.fetchone()[0]

            if view_exists:
                print("✅ vwBUGS view found!")

                # Get sample data (first 5 records)
                cursor.execute("""
                    SELECT TOP 5 [BUG_NUMBER], [NAME], [STATUS], [PRIORITY], [TYPE]
                    FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS]
                    ORDER BY [BUG_NUMBER]
                """)

                sample_data = cursor.fetchall()

                if sample_data:
                    print(f"📋 Found {len(sample_data)} sample records:")
                    print("   BUG_NUMBER | NAME | STATUS | PRIORITY | TYPE")
                    print("   " + "-" * 60)

                    for bug_number, name, status, priority, bug_type in sample_data:
                        name_short = (
                            (name[:30] + "...")
                            if name and len(name) > 30
                            else (name or "N/A")
                        )
                        print(
                            f"   {bug_number or 'N/A':10} | {name_short:30} | {status or 'N/A':8} | {priority or 'N/A':8} | {bug_type or 'N/A'}"
                        )

                    print()

                    # Test the actual query with first bug number
                    first_bug = sample_data[0][0]
                    if first_bug:
                        print(f"🧪 Testing query with BUG_NUMBER: {first_bug}")

                        cursor.execute(splendid_query, (first_bug,))
                        result = cursor.fetchone()

                        if result:
                            print("✅ Query executed successfully!")
                            print("📊 Result fields:")

                            columns = [desc[0] for desc in cursor.description]
                            for i, (col, value) in enumerate(zip(columns, result)):
                                value_str = (
                                    str(value)[:50]
                                    + ("..." if len(str(value)) > 50 else "")
                                    if value
                                    else "NULL"
                                )
                                print(f"   {i + 1:2d}. {col:20} = {value_str}")
                        else:
                            print("⚠️ Query executed but returned no results")
                    else:
                        print("⚠️ First record has no BUG_NUMBER to test with")

                else:
                    print("⚠️ vwBUGS view exists but contains no data")

            else:
                print("❌ vwBUGS view not found!")
                print("💡 Checking for similar views...")

                cursor.execute("""
                    SELECT TABLE_NAME
                    FROM INFORMATION_SCHEMA.VIEWS
                    WHERE TABLE_NAME LIKE '%BUG%' OR TABLE_NAME LIKE '%ISSUE%'
                    ORDER BY TABLE_NAME
                """)

                similar_views = cursor.fetchall()
                if similar_views:
                    print("📋 Found similar views:")
                    for (view_name,) in similar_views:
                        print(f"   • {view_name}")
                else:
                    print("❌ No similar views found")

    except Exception as e:
        print(f"❌ Query test failed: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return False

    return True


if __name__ == "__main__":
    print("🤖 SplendidCRM SQL Server Connection Test")
    print("=" * 60)

    success = test_splendidcrm_connection()

    if success:
        # Test the specific SplendidCRM query
        query_success = test_splendidcrm_query()

        if query_success:
            print("\n✅ Connection and query tests completed successfully!")
            print("💡 Next steps:")
            print("   1. Configure RepoSense AI with your SplendidCRM settings")
            print("   2. Test change request extraction with commit messages")
            print("   3. Verify bug number patterns are detected correctly")
        else:
            print("\n⚠️ Connection successful but query test failed!")
            print("💡 Check view permissions and query syntax")
    else:
        print("\n❌ Connection test failed!")
        print("💡 Please resolve connection issues before proceeding")

    print("\n🔧 To run this test in Docker:")
    print(
        "   docker-compose exec reposense-ai python tools/test_splendidcrm_connection.py"
    )
