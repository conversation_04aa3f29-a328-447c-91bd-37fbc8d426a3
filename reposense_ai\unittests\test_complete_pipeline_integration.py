#!/usr/bin/env python3
"""
Unit tests for Complete Pipeline Integration
Tests the end-to-end flow from change request retrieval to document generation
"""

import os
import sqlite3
import tempfile
import unittest
from datetime import datetime
from unittest.mock import Mock, patch

import pytest

# Import the modules we're testing
try:
    from change_request_service import ChangeRequestService
    from config_manager import ConfigManager
    from document_database import DocumentDatabase
    from models import CommitInfo, Config, RepositoryConfig, SqlConfig
    from unified_document_processor import UnifiedDocumentProcessor

    PIPELINE_AVAILABLE = True
except ImportError as e:
    print(f"Pipeline modules not available: {e}")
    PIPELINE_AVAILABLE = False


class TestCompletePipelineIntegration(unittest.TestCase):
    """Test complete pipeline integration functionality"""

    def setUp(self):
        """Set up test fixtures"""
        if not PIPELINE_AVAILABLE:
            self.skipTest("Pipeline modules not available")

        self.test_db_path = None
        self.change_request_service = None
        self.unified_processor = None
        self.document_db = None

    def tearDown(self):
        """Clean up test fixtures"""
        if self.test_db_path and os.path.exists(self.test_db_path):
            os.unlink(self.test_db_path)

    def create_test_database(self) -> str:
        """Create a temporary test database with change request data"""
        # Create temporary database
        fd, self.test_db_path = tempfile.mkstemp(suffix="_pipeline_test.db")
        os.close(fd)

        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()

        # Create change requests table (matching SQL schema)
        cursor.execute("""
            CREATE TABLE change_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                number VARCHAR(50) UNIQUE NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'OPEN',
                priority VARCHAR(20) DEFAULT 'MEDIUM',
                category VARCHAR(50),
                assigned_to VARCHAR(100),
                assigned_to_tester VARCHAR(100),
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                risk_level VARCHAR(20),
                business_impact TEXT,
                technical_notes TEXT
            )
        """)

        # Insert test change request
        test_cr = {
            "number": "12345",
            "title": "Implement Camera Auto-Capture with Interval Control",
            "description": """Add automatic camera capture functionality with configurable interval settings.
            
Requirements:
- Add interval control (1-3600 seconds)
- Add start/stop button for user control
- Update camera control UI with new controls
- Ensure proper error handling for camera disconnection

Business Justification:
This feature will enable automated surveillance capabilities, reducing manual intervention 
and improving monitoring efficiency for security applications.""",
            "status": "APPROVED",
            "priority": "HIGH",
            "category": "FEATURE_ENHANCEMENT",
            "assigned_to": "fvaneijk",
            "assigned_to_tester": "<EMAIL>",
            "created_date": "2016-06-15 09:00:00",
            "updated_date": "2016-06-20 16:30:00",
            "risk_level": "MEDIUM",
            "business_impact": "High - Enables automated surveillance, reduces manual monitoring costs",
            "technical_notes": "Requires UI changes to CameraControl.Designer.cs and MainForm.Designer.cs. Add timer controls and event handlers.",
        }

        cursor.execute(
            """
            INSERT INTO change_requests
            (number, title, description, status, priority, category, assigned_to, assigned_to_tester,
             created_date, updated_date, risk_level, business_impact, technical_notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                test_cr["number"],
                test_cr["title"],
                test_cr["description"],
                test_cr["status"],
                test_cr["priority"],
                test_cr["category"],
                test_cr["assigned_to"],
                test_cr["assigned_to_tester"],
                test_cr["created_date"],
                test_cr["updated_date"],
                test_cr["risk_level"],
                test_cr["business_impact"],
                test_cr["technical_notes"],
            ),
        )

        conn.commit()
        conn.close()

        return self.test_db_path

    def create_change_request_service(self) -> ChangeRequestService:
        """Create change request service with test database"""
        # Ensure test database is created
        if self.test_db_path is None:
            self.create_test_database()

        # At this point, test_db_path is guaranteed to be a string
        assert self.test_db_path is not None, "Test database path should be set"

        sql_config = SqlConfig(
            enabled=True,
            driver="sqlite",
            database=self.test_db_path,
            change_request_query="""
                SELECT id, number, title, description, priority, status,
                       created_date, assigned_to, assigned_to_tester, category, risk_level,
                       business_impact, technical_notes
                FROM change_requests
                WHERE number = :change_request_number
            """,
        )

        config = Config(sql_config=sql_config)
        return ChangeRequestService(config)

    def create_test_commit(self) -> CommitInfo:
        """Create a test commit that references the change request"""
        return CommitInfo(
            revision="7",
            author="fvaneijk",
            date="2016-06-21 01:44:41",
            message="Implement camera interval control and start button - CR-12345",
            changed_paths=[
                "/CaptureCam/AutoKams/Controls/CameraControl.Designer.cs",
                "/CaptureCam/AutoKams/MainForm.Designer.cs",
            ],
            diff="""@@ -15,6 +15,25 @@ namespace AutoKams.Controls
             this.btnStart = new System.Windows.Forms.Button();
             this.lblStatus = new System.Windows.Forms.Label();
             this.pnlCamera = new System.Windows.Forms.Panel();
+            this.lblInterval = new System.Windows.Forms.Label();
+            this.numInterval = new System.Windows.Forms.NumericUpDown();
+            this.tmrCapture = new System.Windows.Forms.Timer(this.components);
+            ((System.ComponentModel.ISupportInitialize)(this.numInterval)).BeginInit();
             this.SuspendLayout();
             // 
+            // lblInterval
+            // 
+            this.lblInterval.AutoSize = true;
+            this.lblInterval.Location = new System.Drawing.Point(12, 50);
+            this.lblInterval.Name = "lblInterval";
+            this.lblInterval.Size = new System.Drawing.Size(45, 13);
+            this.lblInterval.TabIndex = 3;
+            this.lblInterval.Text = "Interval (seconds):";
+            // 
+            // numInterval
+            // 
+            this.numInterval.Location = new System.Drawing.Point(120, 48);
+            this.numInterval.Maximum = new decimal(new int[] { 3600, 0, 0, 0});
+            this.numInterval.Minimum = new decimal(new int[] { 1, 0, 0, 0});
+            this.numInterval.Name = "numInterval";
+            this.numInterval.Size = new System.Drawing.Size(80, 20);
+            this.numInterval.TabIndex = 4;
+            this.numInterval.Value = new decimal(new int[] { 5, 0, 0, 0});
+            // 
+            // tmrCapture - Timer for automatic capture
+            // 
+            this.tmrCapture.Tick += new System.EventHandler(this.tmrCapture_Tick);""",
            repository_id="visionApi",
            repository_name="visionApi",
        )

    def test_change_request_extraction_and_retrieval(self):
        """Test extraction of change request numbers and retrieval from database"""
        self.create_test_database()
        service = self.create_change_request_service()
        commit = self.create_test_commit()

        # Test extraction of change request numbers from commit message
        cr_numbers = service.extract_change_request_numbers(commit.message)
        self.assertEqual(cr_numbers, ["12345"])

        # Test retrieval of change requests
        change_requests = service.get_multiple_change_requests(cr_numbers)
        self.assertEqual(len(change_requests), 1)

        cr = change_requests[0]
        self.assertEqual(cr.number, "12345")
        self.assertEqual(
            cr.title, "Implement Camera Auto-Capture with Interval Control"
        )
        self.assertEqual(cr.priority, "HIGH")
        self.assertEqual(cr.status, "APPROVED")
        self.assertEqual(cr.risk_level, "MEDIUM")

    @patch("unified_document_processor.UnifiedDocumentProcessor")
    def test_complete_pipeline_processing(self, mock_processor_class):
        """Test complete pipeline from change request to document generation"""
        # Setup test database and service
        self.create_test_database()
        service = self.create_change_request_service()
        commit = self.create_test_commit()

        # Get change requests for this commit
        cr_numbers = service.extract_change_request_numbers(commit.message)
        change_requests = service.get_multiple_change_requests(cr_numbers)
        commit.change_requests = change_requests

        # Mock the unified processor
        mock_processor = Mock()
        mock_processor.process_commit.return_value = True
        mock_processor_class.return_value = mock_processor

        # Create repository config
        repo_config = RepositoryConfig(
            name=commit.repository_name,
            url="file:///test/repo",
            type="svn",
            monitor_all_branches=False,
            last_revision=int(commit.revision) - 1,
        )

        # Mock config manager
        with patch("config_manager.ConfigManager") as mock_config_manager:
            mock_config = Mock()
            mock_config_manager.return_value.load_config.return_value = mock_config

            # Create processor and process commit
            processor = mock_processor_class(config_manager=mock_config_manager())

            # Generate test documentation
            test_documentation = f"""# Revision {commit.revision} - {commit.repository_name}

## Summary
{commit.message}

## Change Request Context
This commit implements change request 12345: {change_requests[0].title}
- Priority: {change_requests[0].priority}
- Status: {change_requests[0].status}
- Category: {change_requests[0].category}
- Description: {change_requests[0].description}

## Technical Details
The changes include:
- Added interval control UI elements
- Added timer functionality for automatic capture
- Updated camera control designer files

## Risk Assessment
{change_requests[0].risk_level} risk based on change request analysis."""

            success = processor.process_commit(commit, repo_config, test_documentation)

            # Verify processing was successful
            self.assertTrue(success)
            mock_processor.process_commit.assert_called_once_with(
                commit, repo_config, test_documentation
            )

    def test_commit_without_change_requests(self):
        """Test processing of commits without change request references"""
        commit = CommitInfo(
            revision="8",
            author="<EMAIL>",
            date="2016-06-22 10:15:30",
            message="Fix minor bug in camera initialization",
            changed_paths=["/CaptureCam/AutoKams/CameraManager.cs"],
            diff="""@@ -45,7 +45,7 @@ namespace AutoKams
             private void InitializeCamera()
             {
-                if (camera == null)
+                if (camera == null || !camera.IsConnected)
                 {
                     camera = new Camera();
                     camera.Connect();""",
            repository_id="visionApi",
            repository_name="visionApi",
            change_requests=[],
        )

        # Verify commit has no change requests
        self.assertEqual(len(commit.change_requests), 0)

        # Verify commit still has all necessary information for processing
        self.assertIsNotNone(commit.message)
        self.assertIsNotNone(commit.diff)
        self.assertGreater(len(commit.changed_paths), 0)
        self.assertIn("bug", commit.message.lower())


if __name__ == "__main__":
    unittest.main()
