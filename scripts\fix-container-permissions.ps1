# Fix Container-Side Permissions for RepoSense AI
# This script fixes permissions INSIDE a running Docker container
# Use this when the container is running but has permission issues with files

param(
    [switch]$Help
)

if ($Help) {
    Write-Host @"
Fix Container-Side Permissions for RepoSense AI

This script fixes permissions inside a running RepoSense AI container.
Use this when the container is running but has permission issues with files.

Usage:
    .\scripts\fix-container-permissions.ps1

Note:
    This script fixes permissions INSIDE the container.
    For host-side permission issues, use: .\scripts\fix-docker-permissions.ps1
"@
    exit 0
}

# Function to write colored output
function Write-Info {
    param([string]$Message)
    Write-Host "[✓] $Message" -ForegroundColor Green
}

function Write-Warn {
    param([string]$Message)
    Write-Host "[!] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[✗] $Message" -ForegroundColor Red
}

function Write-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Blue
}

Write-Host "🔧 RepoSense AI Container Permissions Fix" -ForegroundColor Cyan
Write-Host "This script fixes permissions inside a running RepoSense AI container."
Write-Host ""

# Check if container is running
$containerRunning = docker ps --format "table {{.Names}}" | Select-String "reposense-ai"
if (-not $containerRunning) {
    Write-Error "RepoSense AI container is not running!"
    Write-Host "Please start the container first with: docker-compose up -d reposense-ai"
    exit 1
}

Write-Step "1. Fixing container-side permissions..."

# Fix config.json ownership and permissions
Write-Info "Fixing config.json permissions..."
try {
    docker exec -u root reposense-ai chown appuser:appuser /app/data/config.json 2>$null
    docker exec -u root reposense-ai chmod 664 /app/data/config.json 2>$null
} catch {
    Write-Warn "config.json not found or already correct"
}

# Fix data directory permissions
Write-Info "Fixing data directory permissions..."
docker exec -u root reposense-ai chown -R appuser:appuser /app/data/
docker exec -u root reposense-ai chmod -R 755 /app/data/

# Fix specific file permissions
Write-Info "Fixing database and log file permissions..."
try {
    docker exec -u root reposense-ai chmod 664 /app/data/*.db 2>$null
} catch {
    Write-Info "No database files found"
}

try {
    docker exec -u root reposense-ai chmod 664 /app/data/*.log* 2>$null
} catch {
    Write-Info "No log files found"
}

Write-Step "2. Verifying permissions..."

# Check current permissions
Write-Info "Current permissions:"
try {
    docker exec reposense-ai ls -la /app/data/config.json
} catch {
    Write-Warn "config.json not found"
}

Write-Step "3. Restarting container to apply changes..."
docker-compose restart reposense-ai

# Wait for container to restart
Start-Sleep -Seconds 5

# Verify container is running
$containerRunningAfter = docker ps --format "table {{.Names}}" | Select-String "reposense-ai"
if ($containerRunningAfter) {
    Write-Info "Container restarted successfully"
} else {
    Write-Error "Container failed to restart - check logs with: docker-compose logs reposense-ai"
    exit 1
}

Write-Host ""
Write-Host "✅ Container permissions fixed and container restarted!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Blue
Write-Host "1. Check container logs: docker-compose logs reposense-ai"
Write-Host "2. Verify web interface: http://localhost:5000"
Write-Host ""
Write-Host "💡 Note:" -ForegroundColor Yellow
Write-Host "This script fixes permissions INSIDE the container."
Write-Host "For host-side permission issues, use: .\scripts\fix-docker-permissions.ps1"
