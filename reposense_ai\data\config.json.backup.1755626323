{"users": [{"id": "cc057125-788c-4319-8d24-29e603cddbaa", "username": "admin", "email": "<EMAIL>", "full_name": "System Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-15T23:43:45.427017", "last_modified": "2025-08-15T23:43:45.427030"}, {"id": "8268bacd-7230-4273-99ea-471d99f8a1f1", "username": "manager", "email": "<EMAIL>", "full_name": "Project Manager", "role": "manager", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-15T23:43:45.427134", "last_modified": "2025-08-15T23:43:45.427137"}], "repositories": [{"id": "b32d6910-a982-4fe7-a65e-67cf40bf3c5c", "name": "reposense_cpp_test", "url": "http://sundc:81/svn/reposense_cpp_test", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 17, "last_commit_date": null, "last_processed_time": null, "enabled": true, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 17, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 17, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-19T16:41:39.966136", "total_revisions": null, "processed_revisions": 2, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": [], "risk_aggressiveness": "CONSERVATIVE", "risk_description": ""}, {"id": "c2cd75fc-0e40-4422-af63-496a03484e70", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": null, "last_processed_time": null, "enabled": true, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 4, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 8, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-19T16:41:49.302906", "total_revisions": 4, "processed_revisions": 2, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/README.md"], "risk_aggressiveness": "BALANCED", "risk_description": ""}], "ollama_host": "http://************:11434", "ollama_model": "smollm2:latest", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 300, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "skip_initial_scan": false, "cleanup_orphaned_documents": false, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "mailhog", "smtp_port": 1025, "smtp_username": null, "smtp_password": null, "email_from": "reposense-ai@localhost", "email_recipients": ["<EMAIL>", "<EMAIL>"], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": true, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "75b0da6de4dc560a4594ffc806d3b677945ce1661a367d3e6a4754dabf35b32c", "web_log_entries": 300, "log_cleanup_max_size_mb": 50, "log_cleanup_lines_to_keep": 1000, "log_rotation_max_size_mb": 10, "log_rotation_backup_count": 5}