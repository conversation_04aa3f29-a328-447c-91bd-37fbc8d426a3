Integration to Sonarqube
 
AI based Quality and Security analyses
 
Code review workflow
 
CR integration
 
Pre or Post review
 
AI machine
 
Analyse Mutiple check-in depending on one CR. Launch the analyses when CR is done (new status before test -> AI/Code review)

Take in account parent CR

Modify prompt: "If section of the code change multiple time across check-in always Analyse on the latest checkin"

 Email:
 smtp-relay
 25
 