# SplendidCRM Integration Setup Guide

## Overview

This guide shows how to configure RepoSense AI to integrate with your SplendidCRM system for change request tracking and analysis.

## 🎯 Your SplendidCRM Configuration

### **Database Connection Details**

- **Server**: `splendiddb.public.d490bb5f4eae.database.windows.net,3342`
- **Database**: `SplendidCRM_TriMicro`
- **Username**: `tmwGPT`
- **Password**: `ZaQXa$TP6J`
- **View**: `vwBUGS`

### **Change Request Query with Field Aliases**

**Important**: Use column aliases to map SplendidCRM fields to RepoSense AI expected field names:

```sql
SELECT [BUG_NUMBER] as id,
       [BUG_NUMBER] as number,
       [NAME] as title,
       [DESCRIPTION] as description,
       [PRIORITY] as priority,
       [STATUS] as status,
       [DATES] as created_date,
       [ASSIGNED_TO_NAME] as assigned_to,
       [TYPE] as category,
       [PRIORITY] as risk_level
FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS]
WHERE [BUG_NUMBER] = :change_request_number
```

**Field Mapping Explanation:**

| **RepoSense Field** | **SplendidCRM Column** | **Purpose** |
|---------------------|------------------------|-------------|
| `id` | `[BUG_NUMBER]` | Unique identifier |
| `number` | `[BUG_NUMBER]` | Display number |
| `title` | `[NAME]` | Bug summary/title |
| `description` | `[DESCRIPTION]` | Detailed description |
| `priority` | `[PRIORITY]` | Priority level |
| `status` | `[STATUS]` | Current status |
| `created_date` | `[DATES]` | Creation timestamp |
| `assigned_to` | `[ASSIGNED_TO_NAME]` | Assignee name |
| `category` | `[TYPE]` | Bug category/type |
| `risk_level` | `[PRIORITY]` | Risk assessment |

**Legacy Query (without aliases - DO NOT USE):**

```sql
SELECT [BUG_NUMBER],[NAME],[STATUS],[PRIORITY],[TYPE],[FIXED_IN_RELEASE_RELEASE_GROUP],[FIXED_IN_RELEASE_VERSION],[DESCRIPTION],[ASSIGNED_TO],[CREATED_BY],[MODIFIED_BY],[ASSIGNED_TO_NAME],[TEST_ASSIGNED_TO_NAME_C],[CREATED_BY_NAME],[MODIFIED_BY_NAME],[DATES] FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS] WHERE [BUG_NUMBER] = :change_request_number
```

*Note: This query will result in "Not specified" values in revision documents.*

## 🔧 Configuration Steps

### **Step 1: Test Connection**

First, verify your database connection works:

```bash
# Run the connection test
docker-compose exec reposense-ai python tools/test_splendidcrm_connection.py
```

This will:

- ✅ Test database connectivity
- ✅ Verify the `vwBUGS` view exists
- ✅ Show sample bug records
- ✅ Test your specific query

### **Step 2: Web Interface Configuration**

1. **Navigate to Configuration**: <http://localhost:5001/config>
2. **Enable SQL Integration**: Check the "Enable SQL Integration" box
3. **Configure Database Settings**:

   ```
   Driver: SQL Server
   Host: splendiddb.public.d490bb5f4eae.database.windows.net,3342
   Database: SplendidCRM_TriMicro
   Username: tmwGPT
   Password: password
   Connection Timeout: 30
   Query Timeout: 60
   ```

4. **Set Change Request Query**:

   ```sql
   SELECT [BUG_NUMBER] as number, [NAME] as title, [STATUS] as status, [PRIORITY] as priority, [TYPE] as category, [FIXED_IN_RELEASE_RELEASE_GROUP] as release_group, [FIXED_IN_RELEASE_VERSION] as release_version, [DESCRIPTION] as description, [ASSIGNED_TO_NAME] as assigned_to, [TEST_ASSIGNED_TO_NAME_C] as test_assigned_to, [CREATED_BY_NAME] as created_by, [MODIFIED_BY_NAME] as modified_by, [DATES] as dates FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS] WHERE [BUG_NUMBER] = ?
   ```

5. **Configure Bug Number Patterns**:

   ```regex
   BUG[#\-]\s*(\d+)      # BUG# or BUG- followed by number
   BUG\s+(\d+):          # BUG followed by space and number with colon
   Bug[#\-]\s*(\d+)      # Bug# or Bug- followed by number
   bug[#\-]\s*(\d+)      # bug# or bug- followed by number
   CR[#\-]\s*(\d+)       # CR# or CR- followed by number
   CR\s+(\d+):           # CR followed by space and number with colon
   CR\s+(\d+)            # CR followed by space and number (no colon)
   Issue[#\-]\s*(\d+)    # Issue# or Issue- followed by number
   issue[#\-]\s*(\d+)    # issue# or issue- followed by number
   #(\d+)                # Hash followed by number
   ```

   **Examples that will be detected:**
   - `"CR 21093"` → Extracts `21093`
   - `"BUG-21113: Fixed issue"` → Extracts `21113`
   - `"Issue #21112"` → Extracts `21112`

### **Step 3: Test Integration**

Create test commit messages to verify pattern detection:

```bash
# Test commit messages that should match:
git commit -m "Fixed login issue - BUG#12345"
git commit -m "Updated user interface for Bug-67890"
git commit -m "Performance improvement bug 54321"
git commit -m "Security fix #98765"
```

## 📊 Field Mapping

Your SplendidCRM `vwBUGS` view fields are mapped to RepoSense AI as follows:

| SplendidCRM Field | RepoSense AI Field | Description |
|-------------------|-------------------|-------------|
| `BUG_NUMBER` | `number` | Bug/Change Request ID |
| `NAME` | `title` | Bug title/summary |
| `STATUS` | `status` | Current status |
| `PRIORITY` | `priority` | Priority level |
| `TYPE` | `category` | Bug type/category |
| `DESCRIPTION` | `description` | Detailed description |
| `ASSIGNED_TO_NAME` | `assigned_to` | Assigned developer |
| `FIXED_IN_RELEASE_RELEASE_GROUP` | `release_group` | Target release group |
| `FIXED_IN_RELEASE_VERSION` | `release_version` | Target version |
| `TEST_ASSIGNED_TO_NAME_C` | `test_assigned_to` | Test assignee |
| `CREATED_BY_NAME` | `created_by` | Bug reporter |
| `MODIFIED_BY_NAME` | `modified_by` | Last modifier |
| `DATES` | `dates` | Important dates |

## 🎯 Bug Number Pattern Examples

Your configuration will detect these patterns in commit messages:

- `BUG#12345` → Extracts `12345`
- `Bug-67890` → Extracts `67890`
- `bug 54321` → Extracts `54321`
- `Fixed #98765` → Extracts `98765`
- `Resolves BUG 11111` → Extracts `11111`

## 🔍 Troubleshooting

### **Connection Issues**

```bash
# Check ODBC driver installation
docker exec reposense-ai odbcinst -q -d

# Test basic connectivity
docker exec reposense-ai python -c "
import pyodbc
conn = pyodbc.connect('DRIVER={ODBC Driver 18 for SQL Server};SERVER=splendiddb.public.d490bb5f4eae.database.windows.net,3342;DATABASE=SplendidCRM_TriMicro;UID=tmwGPT;PWD=password;Encrypt=yes;TrustServerCertificate=no;')
print('Connection successful!')
conn.close()
"
```

### **Query Issues**

```bash
# Test the vwBUGS view directly
docker exec reposense-ai python -c "
import pyodbc
conn = pyodbc.connect('DRIVER={ODBC Driver 18 for SQL Server};SERVER=splendiddb.public.d490bb5f4eae.database.windows.net,3342;DATABASE=SplendidCRM_TriMicro;UID=tmwGPT;PWD=password;Encrypt=yes;TrustServerCertificate=no;')
cursor = conn.cursor()
cursor.execute('SELECT TOP 1 [BUG_NUMBER], [NAME] FROM [SplendidCRM_TriMicro].[dbo].[vwBUGS]')
result = cursor.fetchone()
print('Sample record:', result)
conn.close()
"
```

### **Permission Issues**

If you get permission errors:

1. Verify `tmwGPT` user has `SELECT` permissions on `vwBUGS`
2. Check if the view exists: `SELECT * FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = 'vwBUGS'`
3. Ensure database name is correct: `SplendidCRM_TriMicro`

## 🚀 Production Deployment

### **Security Considerations**

1. **Use dedicated read-only user** for RepoSense AI
2. **Limit permissions** to only the `vwBUGS` view
3. **Consider connection pooling** for high-volume scenarios
4. **Monitor connection usage** and timeouts

### **Performance Optimization**

1. **Index BUG_NUMBER** field if not already indexed
2. **Set appropriate timeouts** (30s connection, 60s query)
3. **Monitor query performance** in SplendidCRM
4. **Consider caching** for frequently accessed bugs

## 📋 Verification Checklist

- ✅ **Database connection** works from Docker container
- ✅ **vwBUGS view** is accessible with `tmwGPT` user
- ✅ **Sample query** returns expected data
- ✅ **Bug number patterns** are configured correctly
- ✅ **RepoSense AI configuration** is saved
- ✅ **Test commit messages** trigger change request lookup
- ✅ **Bug information** appears in document analysis

## 📞 Support

If you encounter issues:

1. **Run the test script**: `python tools/test_splendidcrm_connection.py`
2. **Check RepoSense AI logs**: `docker logs reposense-ai`
3. **Verify SplendidCRM permissions**: Ensure `tmwGPT` can access `vwBUGS`
4. **Test query manually**: Use SQL Server Management Studio or similar tool

Your SplendidCRM integration is now ready to provide rich change request context for your code analysis!
