#!/bin/bash
# Fix Container-Side Permissions for RepoSense AI
# This script fixes permissions INSIDE a running Docker container
# Use this when the container is running but has permission issues with files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔧 RepoSense AI Container Permissions Fix${NC}"
echo "This script fixes permissions inside a running RepoSense AI container."
echo

# Function to log messages
log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if container is running
if ! docker ps | grep -q "reposense-ai"; then
    log_error "RepoSense AI container is not running!"
    echo "Please start the container first with: docker-compose up -d reposense-ai"
    exit 1
fi

log_step "1. Fixing container-side permissions..."

# Fix config.json ownership and permissions
log_info "Fixing config.json permissions..."
docker exec -u root reposense-ai chown appuser:appuser /app/data/config.json 2>/dev/null || log_warn "config.json not found or already correct"
docker exec -u root reposense-ai chmod 664 /app/data/config.json 2>/dev/null || log_warn "config.json not found or already correct"

# Fix data directory permissions
log_info "Fixing data directory permissions..."
docker exec -u root reposense-ai chown -R appuser:appuser /app/data/
docker exec -u root reposense-ai chmod -R 755 /app/data/

# Fix specific file permissions
log_info "Fixing database and log file permissions..."
docker exec -u root reposense-ai chmod 664 /app/data/*.db 2>/dev/null || log_info "No database files found"
docker exec -u root reposense-ai chmod 664 /app/data/*.log* 2>/dev/null || log_info "No log files found"

log_step "2. Verifying permissions..."

# Check current permissions
log_info "Current permissions:"
docker exec reposense-ai ls -la /app/data/config.json 2>/dev/null || log_warn "config.json not found"

log_step "3. Restarting container to apply changes..."
docker-compose restart reposense-ai

# Wait for container to restart
sleep 5

# Verify container is running
if docker ps | grep -q "reposense-ai"; then
    log_info "Container restarted successfully"
else
    log_error "Container failed to restart - check logs with: docker-compose logs reposense-ai"
    exit 1
fi

echo
echo -e "${GREEN}✅ Container permissions fixed and container restarted!${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Check container logs: docker-compose logs reposense-ai"
echo "2. Verify web interface: http://localhost:5000"
echo
echo -e "${YELLOW}💡 Note:${NC}"
echo "This script fixes permissions INSIDE the container."
echo "For host-side permission issues, use: ./scripts/fix-docker-permissions.sh"
