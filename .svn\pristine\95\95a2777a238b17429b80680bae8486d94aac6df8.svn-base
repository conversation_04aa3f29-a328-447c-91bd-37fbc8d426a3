# Change Request Integration Setup Guide

## Overview

This guide walks you through setting up change request integration in RepoSense AI, allowing automatic correlation between code changes and your organization's change management system.

## Prerequisites

- RepoSense AI Enterprise edition
- Access to your change request database (MySQL, PostgreSQL, SQL Server, Oracle)
- Database user with read permissions on change request tables
- Knowledge of your change request numbering format
- **For SQL Server**: Microsoft ODBC Driver 18 for SQL Server (automatically installed in Docker)

## Quick Setup

### 1. Database Preparation

Create a dedicated database user for RepoSense AI:

```sql
-- MySQL Example
CREATE USER 'reposense'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT ON change_requests.* TO 'reposense'@'%';
FLUSH PRIVILEGES;

-- SQL Server Example
CREATE LOGIN reposense_user WITH PASSWORD = 'SecurePassword123!';
USE change_requests;
CREATE USER reposense_user FOR LOGIN reposense_user;
ALTER ROLE db_datareader ADD MEMBER reposense_user;

-- PostgreSQL Example
CREATE USER reposense WITH PASSWORD 'secure_password';
GRANT SELECT ON ALL TABLES IN SCHEMA public TO reposense;
```

### 2. Docker Compose Configuration

Add MySQL test database to your docker-compose.yml:

```yaml
services:
  mysql-test:
    image: mysql:8.0
    container_name: reposense-ai-mysql-test
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: testpassword
      MYSQL_DATABASE: change_requests
      MYSQL_USER: reposense
      MYSQL_PASSWORD: reposense123
    ports:
      - "3306:3306"
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ./test-data/mysql-init:/docker-entrypoint-initdb.d
    networks:
      - reposense-ai-network

  adminer:
    image: adminer:latest
    container_name: reposense-ai-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - reposense-ai-network
    depends_on:
      - mysql-test

volumes:
  mysql_test_data:
    driver: local
```

### 3. Web Interface Configuration

1. Navigate to RepoSense AI Configuration page
2. Scroll to "SQL Integration" section
3. Enable SQL integration
4. Configure connection settings:
   - **Host**: Your database server hostname (for custom ports: `server.com,3342`)
   - **Port**: Database port (3306 for MySQL, 5432 for PostgreSQL, 1433 for SQL Server)
   - **Database**: Database name containing change requests
   - **Username**: Database user with read permissions
   - **Password**: Database user password
   - **Driver**: Database type (mysql, postgresql, mssql, oracle)

**SQL Server Specific Notes:**
- For Azure SQL Database: Use format `your-server.database.windows.net,1433`
- For custom ports: Include port in hostname like `server.com,3342`
- Connection uses ODBC Driver 18 with encryption enabled
- Supports both SQL Server Authentication and Windows Authentication

### 4. Query Configuration

Configure the SQL query to retrieve change request data:

```sql
SELECT id, number, title, description, priority, status,
       created_date, assigned_to, category, risk_level
FROM change_requests
WHERE number = :change_request_number
```

**Required Fields:**
- `number`: Change request identifier
- `title`: Brief description
- `priority`: Priority level (HIGH, MEDIUM, LOW)
- `status`: Current status

**Optional Fields:**
- `description`: Detailed description
- `category`: Change category
- `risk_level`: Pre-assessed risk level
- `assigned_to`: Assignee
- `created_date`: Creation timestamp

### 5. Pattern Configuration

Set up regex patterns to extract change request numbers from commit messages:

```
CR[#\-\s]*(\d+)
Change[#\-\s]*(\d+)
Request[#\-\s]*(\d+)
Ticket[#\-\s]*(\d+)
Issue[#\-\s]*(\d+)
#(\d+)
```

### 6. Testing Configuration

Use the built-in test functionality:

1. Click "Test Connection" to verify database connectivity
2. Enter a test change request number
3. Click "Test Query" to verify data retrieval
4. Review the returned data structure

### 7. Running Automated Tests

RepoSense AI includes a comprehensive test suite for change request integration:

```bash
# Run all change request integration tests
cd reposense_ai
python -m pytest unittests/test_change_request_integration.py -v

# Run specific test categories
python -m pytest unittests/test_change_request_integration.py::TestChangeRequestIntegration::test_change_request_retrieval -v
python -m pytest unittests/test_change_request_integration.py::TestChangeRequestIntegration::test_change_request_number_extraction -v
```

**Test Coverage Areas:**
- **Pattern Extraction**: Validates regex patterns with various commit message formats
- **Database Connectivity**: Tests connection and query execution with SQLite test database
- **Data Retrieval**: Tests single and multiple change request lookups
- **Integration Testing**: End-to-end testing with mock LLM integration
- **Type Safety**: Ensures all type annotations are correct for production deployment

## Advanced Configuration

### Field Mappings

Map database fields to RepoSense AI fields:

```yaml
field_mappings:
  risk_level_field: "risk_level"
  priority_field: "priority"
  category_field: "category"
  status_field: "status"
```

### Value Mappings

Map database values to RepoSense AI risk levels:

```yaml
value_mappings:
  priority_to_risk:
    "CRITICAL": "HIGH"
    "HIGH": "HIGH"
    "MEDIUM": "MEDIUM"
    "LOW": "LOW"
  category_risk_boost:
    "SECURITY": "HIGH"
    "PERFORMANCE": "MEDIUM"
    "UI": "LOW"
```

### Risk Analysis Configuration

Configure risk assessment parameters:

```yaml
risk_analysis_config:
  supported_risk_levels: ["CRITICAL", "HIGH", "MEDIUM", "LOW"]
  default_risk_level: "MEDIUM"
  confidence_threshold: 0.3
  base_confidence: 0.6
  max_confidence: 0.9
  category_weight: 0.3
```

### Display Configuration

Customize how change request data appears in documentation:

```yaml
summary_display_config:
  enabled: true
  section_title: "Change Request Summary"
  display_fields:
    - "number"
    - "title"
    - "priority"
    - "status"
    - "risk_level"
    - "category"
    - "assigned_to"
    - "created_date"
    - "description"
  field_labels:
    number: "Request Number"
    title: "Title"
    priority: "Priority"
    status: "Status"
    risk_level: "Risk Level"
    category: "Category"
    assigned_to: "Assigned To"
    created_date: "Created Date"
    description: "Description"
  description_max_length: 300
  date_format: "%Y-%m-%d"
  show_empty_fields: false
```

## Troubleshooting

### Connection Issues

1. **Database Connectivity**: Verify network access and firewall rules
2. **Authentication**: Check username/password and user permissions
3. **SSL/TLS**: Configure SSL settings if required by your database

### Query Issues

1. **Table Structure**: Verify table and column names match your schema
2. **Parameter Binding**: Ensure `:change_request_number` parameter is used correctly
3. **Data Types**: Check that returned data types match expected formats

### Pattern Matching

1. **Regex Testing**: Use online regex testers to validate patterns
2. **Case Sensitivity**: Consider case variations in commit messages
3. **Multiple Patterns**: Test with various commit message formats

### Testing Issues

1. **Test Database Creation**: Ensure SQLite is available for test database creation
2. **Type Checking**: Run `mypy` or similar type checker to validate type annotations
3. **Test Dependencies**: Verify all required test dependencies are installed:
   ```bash
   pip install pytest pytest-mock
   ```
4. **Mock Testing**: If LLM integration tests fail, verify mock objects are properly configured
5. **Test Data**: Ensure test database contains expected sample data for validation

## Performance Optimization

### Database Indexing

Create indexes on frequently queried columns:

```sql
CREATE INDEX idx_change_requests_number ON change_requests(number);
CREATE INDEX idx_change_requests_status ON change_requests(status);
```

### Connection Pooling

Configure connection timeouts for optimal performance:

```yaml
connection_timeout: 30  # seconds
query_timeout: 60       # seconds
```

### Caching

RepoSense AI automatically caches change request data to minimize database queries.

## Security Considerations

1. **Least Privilege**: Grant only SELECT permissions to RepoSense AI user
2. **Network Security**: Use VPN or private networks for database access
3. **Password Security**: Use strong passwords and consider password rotation
4. **Audit Logging**: Enable database audit logging for compliance

## Support

For additional assistance with change request integration:

1. Check the troubleshooting section above
2. Review system logs for detailed error messages
3. Contact enterprise support for custom configuration assistance
4. Join our community forum for peer support
