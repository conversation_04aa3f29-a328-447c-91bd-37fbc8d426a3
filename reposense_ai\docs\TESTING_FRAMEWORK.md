# RepoSense AI Testing Framework

## Overview

RepoSense AI includes a comprehensive testing framework designed to ensure production reliability, type safety, and robust integration capabilities. The testing suite covers all major functionality including LDAP integration, change request integration, and core repository monitoring features.

## Test Structure

### Unit Tests Location
```
reposense_ai/unittests/
├── test_change_request_integration.py    # Change request integration tests
├── test_complete_pipeline_integration.py # End-to-end pipeline tests
├── test_ldap_integration.py             # LDAP authentication tests
└── TEST_INTEGRATION_SUMMARY.md          # Test migration summary
```

### Test Categories

#### 1. Change Request Integration Tests
**File**: `test_change_request_integration.py`

**Test Classes:**
- `TestChangeRequestIntegration`: Core change request functionality
- `TestDocumentIdConsistency`: Document ID generation validation
- `TestPromptImprovements`: AI prompt quality assurance

**Key Test Methods:**
- `test_change_request_number_extraction()`: Validates regex pattern matching
- `test_change_request_retrieval()`: Tests database connectivity and data retrieval
- `test_multiple_change_request_retrieval()`: Tests bulk change request operations
- `test_commit_with_change_requests()`: Tests commit object integration
- `test_llm_analysis_with_change_requests()`: Tests AI documentation generation

#### 2. LDAP Integration Tests
**File**: `test_ldap_integration.py`

**Features Tested:**
- LDAP server connectivity
- User authentication and authorization
- Group-based role mapping
- User synchronization
- Configuration validation

#### 3. Complete Pipeline Tests
**File**: `test_complete_pipeline_integration.py`

**Features Tested:**
- End-to-end repository processing
- Document generation pipeline
- Integration between all components
- Performance and reliability testing

## Running Tests

### Prerequisites
```bash
# Install test dependencies
pip install pytest pytest-mock pytest-cov

# Ensure you're in the correct directory
cd reposense_ai
```

### Basic Test Execution
```bash
# Run all tests
python -m pytest unittests/ -v

# Run specific test file
python -m pytest unittests/test_change_request_integration.py -v

# Run specific test class
python -m pytest unittests/test_change_request_integration.py::TestChangeRequestIntegration -v

# Run specific test method
python -m pytest unittests/test_change_request_integration.py::TestChangeRequestIntegration::test_change_request_retrieval -v
```

### Advanced Test Options
```bash
# Run with coverage reporting
python -m pytest unittests/ --cov=. --cov-report=html

# Run with detailed output
python -m pytest unittests/ -v -s

# Run tests in parallel
python -m pytest unittests/ -n auto

# Run only failed tests from last run
python -m pytest unittests/ --lf
```

## Test Database Setup

### SQLite Test Databases
The testing framework automatically creates temporary SQLite databases for testing:

```python
def create_test_database(self) -> str:
    """Create a temporary test database with sample change request data"""
    fd, self.test_db_path = tempfile.mkstemp(suffix="_cr_test.db")
    # ... database setup code
```

**Sample Test Data:**
- Change request CR-2024-001: Camera control interval feature
- Change request 12345: Database performance optimization
- Various status, priority, and category combinations

### Test Data Structure
```sql
CREATE TABLE change_requests (
    id INTEGER PRIMARY KEY,
    number VARCHAR(50) UNIQUE,
    title TEXT,
    description TEXT,
    status VARCHAR(20),
    priority VARCHAR(20),
    category VARCHAR(50),
    assigned_to VARCHAR(100),
    created_date DATETIME,
    updated_date DATETIME,
    risk_level VARCHAR(20),
    business_impact TEXT,
    technical_notes TEXT
)
```

## Type Safety and Validation

### Type Checking Integration
The testing framework includes comprehensive type checking to ensure production reliability:

```python
# Type assertions for runtime safety
assert self.test_db_path is not None, "Test database path should be set"
assert cr_info is not None  # Type assertion for type checker
```

### Supported Type Validations
- Database connection parameters
- Change request data structures
- Configuration objects
- API response formats
- Mock object interfaces

## Mock Testing Framework

### LLM Integration Mocking
```python
@patch("ollama_client.OllamaClient")
def test_llm_analysis_with_change_requests(self, mock_ollama_class):
    """Test LLM analysis with change request context"""
    mock_ollama = Mock()
    mock_ollama.test_connection.return_value = True
    mock_ollama.generate_documentation.return_value = """# Camera Interval Control Implementation
    
    ## Summary
    Implementation of camera interval control feature as requested in CR-2024-001.
    """
```

### Mock Validation
- Verifies AI-generated content includes change request context
- Validates absence of placeholder text in generated documentation
- Tests error handling and edge cases

## Continuous Integration

### Automated Testing Pipeline
```yaml
# Example CI configuration
test:
  script:
    - pip install -r requirements.txt
    - python -m pytest unittests/ --cov=. --cov-report=xml
    - mypy reposense_ai/ --ignore-missing-imports
```

### Quality Gates
- **Test Coverage**: Minimum 80% code coverage required
- **Type Safety**: All type annotations must pass mypy validation
- **Integration Tests**: All database and external service integrations must pass
- **Performance Tests**: Response times must meet SLA requirements

## Troubleshooting

### Common Issues

#### Test Database Creation Failures
```bash
# Ensure SQLite is available
python -c "import sqlite3; print('SQLite available')"

# Check file permissions
ls -la /tmp/  # On Unix systems
```

#### Type Checking Errors
```bash
# Run type checker manually
mypy reposense_ai/unittests/test_change_request_integration.py

# Install type stubs if needed
pip install types-requests types-PyMySQL
```

#### Mock Testing Issues
```bash
# Verify mock dependencies
pip install pytest-mock

# Debug mock calls
python -m pytest unittests/ -v -s --capture=no
```

### Performance Issues
- **Slow Tests**: Use `pytest-xdist` for parallel execution
- **Memory Usage**: Monitor test database cleanup in tearDown methods
- **Network Timeouts**: Adjust timeout values for external service tests

## Best Practices

### Test Development Guidelines
1. **Isolation**: Each test should be independent and not rely on other tests
2. **Cleanup**: Always clean up test databases and temporary files
3. **Mocking**: Mock external dependencies (databases, APIs, LLM services)
4. **Assertions**: Use descriptive assertion messages for better debugging
5. **Type Safety**: Include type assertions where needed for production reliability

### Test Data Management
1. **Realistic Data**: Use realistic test data that mirrors production scenarios
2. **Edge Cases**: Include edge cases and error conditions in test data
3. **Data Variety**: Test with various data formats and structures
4. **Cleanup**: Ensure test data doesn't persist between test runs

### Documentation
1. **Test Documentation**: Document complex test scenarios and expected outcomes
2. **Setup Instructions**: Provide clear setup instructions for new developers
3. **Troubleshooting**: Maintain troubleshooting guides for common issues
4. **Coverage Reports**: Generate and review coverage reports regularly

## Support

For testing-related issues:
1. Check the troubleshooting section above
2. Review test logs for detailed error information
3. Verify all dependencies are installed correctly
4. Contact the development team for assistance with complex testing scenarios
