# Docker Permissions Fix Script for RepoSense AI (Windows/PowerShell)
# This script fixes common Docker volume permission issues on Windows

param(
    [switch]$Help
)

if ($Help) {
    Write-Host @"
Docker Permissions Fix Script for RepoSense AI

This script fixes Docker volume permission issues for RepoSense AI on Windows.
It handles host-side permission setup before the container starts.

Usage:
    .\scripts\fix-docker-permissions.ps1

Note:
    This script fixes permissions on the HOST side (Windows).
    For container-side permission issues, use: .\scripts\fix-container-permissions.ps1
"@
    exit 0
}

# Function to write colored output
function Write-Info {
    param([string]$Message)
    Write-Host "[✓] $Message" -ForegroundColor Green
}

function Write-Warn {
    param([string]$Message)
    Write-Host "[!] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[✗] $Message" -ForegroundColor Red
}

function Write-Step {
    param([string]$Message)
    Write-Host "[STEP] $Message" -ForegroundColor Blue
}

Write-Host "🔧 RepoSense AI Docker Permissions Fix (Windows)" -ForegroundColor Cyan
Write-Host "This script fixes Docker volume permission issues for RepoSense AI on Windows."
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "reposense_ai")) {
    Write-Error "reposense_ai directory not found. Please run this script from the parent directory."
    exit 1
}

Write-Step "1. Checking current directory structure..."

# Check current directories
if (Test-Path "reposense_ai\data") {
    Write-Info "Found data directory: reposense_ai\data"
} else {
    Write-Warn "Data directory does not exist - will be created"
}

if (Test-Path "reposense_ai\logs") {
    Write-Info "Found logs directory: reposense_ai\logs"
} else {
    Write-Warn "Logs directory does not exist - will be created"
}

Write-Step "2. Stopping RepoSense AI container (if running)..."

# Check if container is running
$containerRunning = docker-compose ps reposense-ai | Select-String "Up"
if ($containerRunning) {
    Write-Info "Stopping RepoSense AI container..."
    docker-compose stop reposense-ai
    $ContainerWasRunning = $true
} else {
    Write-Info "Container is not running"
    $ContainerWasRunning = $false
}

Write-Step "3. Creating directories and setting permissions..."

# Create directories if they don't exist
$directories = @(
    "reposense_ai\data",
    "reposense_ai\logs", 
    "reposense_ai\data\output",
    "reposense_ai\data\cache"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Info "Created directory: $dir"
    }
}

Write-Info "Created necessary directories"

# On Windows with Docker Desktop, permissions are typically handled by Docker Desktop
# But we can ensure the directories are accessible
Write-Info "Setting Windows directory permissions..."

try {
    # Give full control to current user and system
    $acl = Get-Acl "reposense_ai"
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
        [System.Security.Principal.WindowsIdentity]::GetCurrent().Name,
        "FullControl",
        "ContainerInherit,ObjectInherit",
        "None",
        "Allow"
    )
    $acl.SetAccessRule($accessRule)
    Set-Acl "reposense_ai" $acl
    Write-Info "Set Windows permissions for current user"
} catch {
    Write-Warn "Could not set Windows permissions: $_"
    Write-Info "This is usually not a problem with Docker Desktop"
}

Write-Step "4. Verifying setup..."

# Verify directories exist
$allDirsExist = $true
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Info "Verified: $dir"
    } else {
        Write-Error "Missing: $dir"
        $allDirsExist = $false
    }
}

if ($allDirsExist) {
    Write-Info "All directories created successfully!"
} else {
    Write-Error "Some directories are missing!"
    exit 1
}

Write-Step "5. Restarting container (if it was running)..."

if ($ContainerWasRunning) {
    Write-Info "Restarting RepoSense AI container..."
    docker-compose up -d reposense-ai
    
    # Wait a moment for container to start
    Start-Sleep -Seconds 5
    
    # Check if container started successfully
    $containerRunningAfter = docker-compose ps reposense-ai | Select-String "Up"
    if ($containerRunningAfter) {
        Write-Info "Container restarted successfully"
    } else {
        Write-Warn "Container may have issues starting - check logs with: docker-compose logs reposense-ai"
    }
} else {
    Write-Info "Container was not running - you can start it with: docker-compose up -d reposense-ai"
}

Write-Host ""
Write-Host "✅ Docker permissions fix completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Blue
Write-Host "1. Check container logs: docker-compose logs reposense-ai"
Write-Host "2. Verify web interface: http://localhost:5000"
Write-Host "3. If issues persist, check the container logs for other errors"
Write-Host ""
Write-Host "💡 Windows Docker Notes:" -ForegroundColor Yellow
Write-Host "- Docker Desktop typically handles volume permissions automatically"
Write-Host "- If you still have permission issues, try restarting Docker Desktop"
Write-Host "- For container-side issues, use: .\scripts\fix-container-permissions.ps1"
