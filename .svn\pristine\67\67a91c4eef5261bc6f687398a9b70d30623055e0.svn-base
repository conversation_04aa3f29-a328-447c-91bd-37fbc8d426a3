-- SQL Server initialization script for change request integration testing
-- This script creates the test database structure and sample data

USE master;
GO

-- Create change_requests database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'change_requests')
BEGIN
    CREATE DATABASE change_requests;
END
GO

USE change_requests;
GO

-- Create change_requests table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='change_requests' AND xtype='U')
BEGIN
    CREATE TABLE change_requests (
        id INT IDENTITY(1,1) PRIMARY KEY,
        number NVARCHAR(50) NOT NULL UNIQUE,
        title NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX),
        priority NVARCHAR(20) DEFAULT 'MEDIUM',
        status NVARCHAR(20) DEFAULT 'OPEN',
        created_date DATETIME2 DEFAULT GETDATE(),
        assigned_to NVARCHAR(100),
        category NVARCHAR(50),
        risk_level NVARCHAR(20) DEFAULT 'LOW'
    );
END
GO

-- Insert sample test data
IF NOT EXISTS (SELECT * FROM change_requests WHERE number = 'CR-2024-001')
BEGIN
    INSERT INTO change_requests (number, title, description, priority, status, assigned_to, category, risk_level)
    VALUES 
        ('CR-2024-001', 'Database Schema Update', 'Update user table to include new authentication fields', 'HIGH', 'APPROVED', '<EMAIL>', 'DATABASE', 'MEDIUM'),
        ('CR-2024-002', 'API Security Enhancement', 'Implement OAuth2 authentication for REST API endpoints', 'CRITICAL', 'IN_PROGRESS', '<EMAIL>', 'SECURITY', 'HIGH'),
        ('CR-2024-003', 'UI Component Refactoring', 'Refactor legacy jQuery components to React', 'MEDIUM', 'OPEN', '<EMAIL>', 'FRONTEND', 'LOW'),
        ('CR-2024-004', 'Performance Optimization', 'Optimize database queries for reporting module', 'HIGH', 'TESTING', '<EMAIL>', 'PERFORMANCE', 'MEDIUM'),
        ('CR-2024-005', 'Third-party Integration', 'Integrate with new payment processing service', 'CRITICAL', 'APPROVED', '<EMAIL>', 'INTEGRATION', 'HIGH');
END
GO

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_change_requests_number')
BEGIN
    CREATE INDEX IX_change_requests_number ON change_requests(number);
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_change_requests_status')
BEGIN
    CREATE INDEX IX_change_requests_status ON change_requests(status);
END
GO

-- Create a test user for RepoSense AI (optional - for production use proper authentication)
IF NOT EXISTS (SELECT * FROM sys.server_principals WHERE name = 'reposense_user')
BEGIN
    CREATE LOGIN reposense_user WITH PASSWORD = 'RepoSense@123';
END
GO

USE change_requests;
GO

IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'reposense_user')
BEGIN
    CREATE USER reposense_user FOR LOGIN reposense_user;
    ALTER ROLE db_datareader ADD MEMBER reposense_user;
END
GO

-- Verify the setup
SELECT 
    'Setup completed successfully. Sample data:' as message,
    COUNT(*) as total_records 
FROM change_requests;
GO

SELECT TOP 3 number, title, priority, status FROM change_requests ORDER BY created_date DESC;
GO

PRINT 'SQL Server change request integration test database initialized successfully!';
GO
