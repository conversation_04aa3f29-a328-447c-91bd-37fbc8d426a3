# Development Dockerfile for RepoSense AI
# Optimized for development with hot reload and debugging capabilities

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV REPOSENSE_AI_ENV=development

# Install system dependencies
RUN apt-get update && apt-get install -y \
    subversion \
    curl \
    git \
    vim \
    nano \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create non-root user for development
RUN groupadd -r appuser && useradd -r -g appuser -m appuser

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-cov \
    black \
    flake8 \
    mypy \
    ipython \
    debugpy

# Copy application code
COPY . .

# Copy and set up credential copying script
COPY copy-svn-credentials.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/copy-svn-credentials.sh

# Create data directories
RUN mkdir -p /app/data/logs /app/data/output/repositories

# Set permissions
RUN chown -R appuser:appuser /app
RUN chmod +x /app/*.py

# Switch to non-root user
USER appuser

# Set Flask development environment
ENV FLASK_ENV=development
ENV FLASK_DEBUG=1
ENV PYTHONUNBUFFERED=1

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000', timeout=5)" || exit 1

# Development command with hot reload and credential copying
CMD ["/usr/local/bin/copy-svn-credentials.sh", "python", "-u", "reposense_ai_app.py"]
