#!/usr/bin/env python3
"""
Change Request Service for RepoSense AI

This service handles integration with SQL databases to retrieve change request
information based on commit messages. It supports multiple database types and
configurable query patterns.
"""

import logging
import re
from contextlib import contextmanager
from datetime import datetime
from typing import List, Optional

from models import ChangeRequestInfo, Config, SqlConfig


class ChangeRequestService:
    """Service for retrieving change request information from SQL databases"""

    def __init__(self, config: Config):
        self.config = config.sql_config
        self.logger = logging.getLogger(__name__)
        self._connection_pool = None

        # Import database drivers based on configuration
        self._db_module = None
        if self.config.enabled:
            self._initialize_database_driver()

    def _initialize_database_driver(self):
        """Initialize the appropriate database driver based on configuration"""
        try:
            if self.config.driver.lower() == "mysql":
                import pymysql

                self._db_module = pymysql
                self.logger.info(
                    "Initialized MySQL driver for change request integration"
                )

            elif self.config.driver.lower() == "postgresql":
                import psycopg2
                import psycopg2.extras

                self._db_module = psycopg2
                self.logger.info(
                    "Initialized PostgreSQL driver for change request integration"
                )

            elif self.config.driver.lower() == "sqlite":
                import sqlite3

                self._db_module = sqlite3
                self.logger.info(
                    "Initialized SQLite driver for change request integration"
                )

            elif self.config.driver.lower() == "mssql":
                import pyodbc

                self._db_module = pyodbc
                self.logger.info(
                    "Initialized SQL Server driver for change request integration"
                )

            else:
                self.logger.error(f"Unsupported database driver: {self.config.driver}")
                self.config.enabled = False

        except ImportError as e:
            self.logger.error(
                f"Failed to import database driver for {self.config.driver}: {e}"
            )
            self.logger.error("Please install the required database driver package")
            self.config.enabled = False

    def extract_change_request_numbers(self, commit_message: str) -> List[str]:
        """Extract change request numbers from commit message using configured patterns"""
        if not commit_message:
            return []

        numbers = []
        for pattern in self.config.change_request_patterns:
            try:
                matches = re.findall(pattern, commit_message, re.IGNORECASE)
                numbers.extend(matches)
            except re.error as e:
                self.logger.warning(f"Invalid regex pattern '{pattern}': {e}")

        # Remove duplicates while preserving order
        unique_numbers = []
        seen = set()
        for number in numbers:
            if number not in seen:
                unique_numbers.append(number)
                seen.add(number)

        if unique_numbers:
            self.logger.debug(
                f"Extracted change request numbers from commit message: {unique_numbers}"
            )

        return unique_numbers

    @contextmanager
    def _get_connection(self):
        """Get database connection with proper error handling"""
        if not self.config.enabled or not self._db_module:
            raise RuntimeError(
                "Change request integration is not enabled or configured"
            )

        connection = None
        try:
            if self.config.driver.lower() == "mysql":
                connection = self._db_module.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database,
                    connect_timeout=self.config.connection_timeout,
                    charset="utf8mb4",
                )

            elif self.config.driver.lower() == "postgresql":
                connection = self._db_module.connect(
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    database=self.config.database,
                    connect_timeout=self.config.connection_timeout,
                )

            elif self.config.driver.lower() == "sqlite":
                connection = self._db_module.connect(
                    self.config.database, timeout=self.config.connection_timeout
                )

            elif self.config.driver.lower() == "mssql":
                connection_string = (
                    f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                    f"SERVER={self.config.host},{self.config.port};"
                    f"DATABASE={self.config.database};"
                    f"UID={self.config.username};"
                    f"PWD={self.config.password};"
                    f"Encrypt=yes;"
                    f"TrustServerCertificate=no;"
                    f"Connection Timeout={self.config.connection_timeout};"
                )
                connection = self._db_module.connect(connection_string)

            yield connection

        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            raise
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    self.logger.warning(f"Error closing database connection: {e}")

    def get_change_request_info(
        self, change_request_number: str
    ) -> Optional[ChangeRequestInfo]:
        """Retrieve change request information from SQL database"""
        if not self.config.enabled:
            self.logger.debug("Change request integration is disabled")
            return None

        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # Prepare the query with parameter substitution
                query = self.config.change_request_query

                # Execute parameterized query based on database type
                if self.config.driver.lower() == "mysql":
                    # MySQL uses %s for parameters
                    query = query.replace(":change_request_number", "%s")
                    cursor.execute(query, (change_request_number,))
                elif self.config.driver.lower() == "postgresql":
                    # PostgreSQL uses %s for parameters
                    query = query.replace(":change_request_number", "%s")
                    cursor.execute(query, (change_request_number,))
                elif self.config.driver.lower() in ["sqlite", "mssql"]:
                    # Use positional parameters
                    query = query.replace(":change_request_number", "?")
                    cursor.execute(query, (change_request_number,))

                row = cursor.fetchone()
                cursor.close()

                if row:
                    return self._row_to_change_request(row)
                else:
                    self.logger.debug(
                        f"No change request found for number: {change_request_number}"
                    )
                    return None

        except Exception as e:
            self.logger.error(
                f"Error retrieving change request {change_request_number}: {e}"
            )
            return None

    def _row_to_change_request(self, row) -> ChangeRequestInfo:
        """Convert database row to ChangeRequestInfo object"""
        try:
            # Handle different row formats (tuple vs dict-like)
            if hasattr(row, "keys"):
                # Dict-like row (e.g., from psycopg2.extras.DictCursor)
                data = dict(row)
            else:
                # Tuple row - assume standard column order
                data = {
                    "id": row[0],
                    "number": row[1],
                    "title": row[2],
                    "description": row[3],
                    "priority": row[4],
                    "status": row[5],
                    "created_date": row[6],
                    "assigned_to": row[7],
                    "category": row[8],
                    "risk_level": row[9],
                }

            # Parse created_date if it's a string
            created_date = data.get("created_date")
            if isinstance(created_date, str):
                try:
                    created_date = datetime.fromisoformat(
                        created_date.replace("Z", "+00:00")
                    )
                except ValueError:
                    created_date = None

            return ChangeRequestInfo(
                id=str(data.get("id", "")),
                number=str(data.get("number", "")),
                title=str(data.get("title", "")),
                description=str(data.get("description", "")),
                priority=str(data.get("priority", "MEDIUM")),
                status=str(data.get("status", "OPEN")),
                created_date=created_date,
                assigned_to=data.get("assigned_to"),
                category=data.get("category"),
                risk_level=data.get("risk_level"),
            )

        except Exception as e:
            self.logger.error(
                f"Error converting database row to ChangeRequestInfo: {e}"
            )
            raise

    def get_multiple_change_requests(
        self, numbers: List[str]
    ) -> List[ChangeRequestInfo]:
        """Retrieve multiple change requests efficiently"""
        if not numbers or not self.config.enabled:
            return []

        results = []
        for number in numbers:
            cr_info = self.get_change_request_info(number)
            if cr_info:
                results.append(cr_info)

        if results:
            self.logger.info(
                f"Retrieved {len(results)} change request records from {len(numbers)} numbers"
            )
        else:
            self.logger.debug(f"No change requests found for numbers: {numbers}")

        return results

    def test_connection(self) -> bool:
        """Test database connection and configuration"""
        if not self.config.enabled:
            self.logger.info("Change request integration is disabled")
            return False

        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # Simple test query
                if self.config.driver.lower() == "mysql":
                    cursor.execute("SELECT VERSION()")
                elif self.config.driver.lower() == "postgresql":
                    cursor.execute("SELECT version()")
                elif self.config.driver.lower() == "sqlite":
                    cursor.execute("SELECT sqlite_version()")
                elif self.config.driver.lower() == "mssql":
                    cursor.execute("SELECT @@VERSION")

                result = cursor.fetchone()
                cursor.close()

                self.logger.info(
                    f"Change request database connection successful: {result[0] if result else 'Connected'}"
                )
                return True

        except Exception as e:
            self.logger.error(f"Change request database connection test failed: {e}")
            return False
