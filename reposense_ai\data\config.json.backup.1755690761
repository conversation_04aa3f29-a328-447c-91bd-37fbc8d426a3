{"server": {"id": "f66b835f-b17a-4d83-a265-3a34d2bb11b7", "name": "home", "description": "Fred svn server", "base_url": "http://sundc:81/svn", "default_username": "fvaneijk", "default_password": "an<PERSON><PERSON><PERSON>", "enabled": true}, "users": [{"id": "55688ffa-21ad-440a-ab62-635f9e7cdbb3", "username": "admin", "email": "<EMAIL>", "full_name": "System Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-19T18:38:26.831310", "last_modified": "2025-08-19T18:38:26.831358"}, {"id": "eed14b02-a85e-40f1-aecb-f2d054e1e640", "username": "manager", "email": "<EMAIL>", "full_name": "Project Manager", "role": "manager", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-19T18:38:26.832556", "last_modified": "2025-08-19T18:38:26.832560"}], "repositories": [{"id": "62103b3e-60c3-4fd8-a1a4-f673713ca6b1", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 8, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": 8, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-19T18:47:24.059931", "total_revisions": 8, "processed_revisions": 8, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/README.md"], "risk_aggressiveness": "BALANCED", "risk_description": ""}, {"id": "1ac88e30-115d-45ba-9fe5-5546323df417", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": false, "scan_by_revision": true, "start_revision": null, "end_revision": null, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": null, "scan_status": "not_started", "scan_started_at": null, "scan_completed_at": null, "total_revisions": null, "processed_revisions": 0, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/README.md"], "risk_aggressiveness": "BALANCED", "risk_description": ""}], "ollama_host": "http://************:11434", "ollama_model": "smollm2:latest", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 300, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "skip_initial_scan": false, "cleanup_orphaned_documents": false, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "mailhog", "smtp_port": 1025, "smtp_username": null, "smtp_password": null, "email_from": "reposense-ai@localhost", "email_recipients": ["<EMAIL>", "<EMAIL>"], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": true, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "75b0da6de4dc560a4594ffc806d3b677945ce1661a367d3e6a4754dabf35b32c", "web_log_entries": 300, "log_cleanup_max_size_mb": 50, "log_cleanup_lines_to_keep": 1000, "log_rotation_max_size_mb": 10, "log_rotation_backup_count": 5}