{"users": [{"id": "8b48118b-635a-4d07-86fa-48ccaa3901fa", "username": "admin", "email": "<EMAIL>", "full_name": "System Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-15T12:48:09.901485", "last_modified": "2025-08-15T12:48:09.901501"}, {"id": "cfc9d44a-9b24-4d0c-8675-d6fad0295d91", "username": "manager", "email": "<EMAIL>", "full_name": "Project Manager", "role": "manager", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": null, "department": null, "created_date": "2025-08-15T12:48:09.911821", "last_modified": "2025-08-15T12:48:09.911839"}], "repositories": [{"id": "c42f3018-3ffc-4434-91df-e1d1d892bb9e", "name": "reposense_cpp_test/trunk", "url": "file:///app/data/repositories/reposense_cpp_test/trunk", "type": "svn", "username": null, "password": null, "last_revision": 0, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": "trunk", "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": false, "scan_by_revision": true, "start_revision": null, "end_revision": null, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": null, "scan_status": "not_started", "scan_started_at": null, "scan_completed_at": null, "total_revisions": null, "processed_revisions": 0, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": [], "risk_aggressiveness": "BALANCED", "risk_description": ""}, {"id": "c04cd403-75d2-4dac-8a44-4205635a78e9", "name": "reposense_branch_tag_test_demo", "url": "http://sundc:81/svn/reposense_branch_tag_test", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 0, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": "trunk", "monitor_all_branches": true, "assigned_users": [], "email_recipients": ["<EMAIL>"], "historical_scan": {"enabled": false, "scan_by_revision": true, "start_revision": null, "end_revision": null, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": null, "scan_status": "not_started", "scan_started_at": null, "scan_completed_at": null, "total_revisions": null, "processed_revisions": 0, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": [], "risk_aggressiveness": "BALANCED", "risk_description": "Demo repository showing multi-branch monitoring capabilities"}, {"id": "261856c6-8967-4324-9a5e-48debdb60942", "name": "test_import_multi_branch", "url": "http://sundc:81/svn/reposense_branch_tag_test", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 0, "last_commit_date": null, "last_processed_time": null, "enabled": true, "branch_path": "trunk", "monitor_all_branches": true, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": false, "scan_by_revision": true, "start_revision": null, "end_revision": null, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": null, "scan_status": "not_started", "scan_started_at": null, "scan_completed_at": null, "total_revisions": null, "processed_revisions": 0, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["README.md", "docs/api.md"], "risk_aggressiveness": "BALANCED", "risk_description": "Test import with multi-branch monitoring"}, {"id": "883b8dc9-143f-4c23-b926-dcae209f90d9", "name": "single_branch_test", "url": "http://sundc:81/svn/reposense_branch_tag_test/trunk", "type": "svn", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 2, "last_commit_date": "2025-08-15T11:58:36.371778", "last_processed_time": "2025-08-15T12:38:37.971597", "enabled": true, "branch_path": "trunk", "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": false, "scan_by_revision": true, "start_revision": null, "end_revision": null, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "force_rescan": false, "last_scanned_revision": null, "scan_status": "not_started", "scan_started_at": null, "scan_completed_at": null, "total_revisions": null, "processed_revisions": 0, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": [], "risk_aggressiveness": "BALANCED", "risk_description": "Test scenario: Single Branch Import"}], "ollama_host": "http://************:11434", "ollama_model": "smollm2:latest", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 300, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "skip_initial_scan": false, "cleanup_orphaned_documents": false, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "mailhog", "smtp_port": 1025, "smtp_username": null, "smtp_password": null, "email_from": "reposense-ai@localhost", "email_recipients": ["<EMAIL>", "<EMAIL>"], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": true, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "d2017e771abff283fd0821fabde0fafa5dc694f41f39c948a9c600c0920f3090", "web_log_entries": 300, "log_cleanup_max_size_mb": 50, "log_cleanup_lines_to_keep": 1000, "log_rotation_max_size_mb": 10, "log_rotation_backup_count": 5}